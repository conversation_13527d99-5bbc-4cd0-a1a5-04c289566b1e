@import url('../index.css');
.project-create-container {
  background: #fff;
  border-radius: 16px;
  padding: 20px;
  width: 600px;
  max-width: 90vw;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  position: relative;
}

.project-create-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.project-create-header h2 {
  font-size: 20px;
  font-weight: 500;
  color: #5B5B5B;
  margin: 0;
}

.project-close-btn {
  background: none;
  border: none;
  font-size: 20px;
  color: #999;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s;
}

.project-close-btn img {
  width: 20px;
  height: 20px;
}

.project-close-btn:hover {
  color: #007bff;
}

.project-create-form {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.project-form-group {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 14px;
}

.project-form-group label {
  font-size: 12px;
  font-weight: 500;
  color: #5B5B5B;
  margin-bottom: 1px;
  display: block;
}

/* Làm cho dấu * trong label có màu đỏ */
.project-form-group label::after {
  content: attr(data-required);
  color: #dc3545;
  margin-left: 2px;
}

.project-form-group label.project-required {
  color: #007bff;
  font-weight: 600;
}
.project-form-group label.project-error {
  color: #dc3545;
  font-weight: 600;
}

.project-form-group input,
.project-form-group textarea {
  padding: 6px 8px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 12px;
  font-family: 'Be Vietnam Pro', Arial, sans-serif;
  line-height: 1.3;
  color: #000;
  background: #fff;
  transition: border-color 0.2s ease;
}

.project-form-group input:focus,
.project-form-group textarea:focus {
  outline: none;
  border-color: #007bff;
}

.project-form-group input::placeholder,
.project-form-group textarea::placeholder {
  color: #999;
}

.project-form-group textarea {
  resize: vertical;
  min-height: 32px;
  max-height: 50px;
}

.project-form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-bottom: 0;
}

.project-form-row.project-three-columns {
  grid-template-columns: 1fr 1fr 1fr;
  gap: 8px;
}

.project-form-row.project-three-columns .project-form-group select {
  font-size: 12px;
  padding: 6px 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.project-form-row.project-three-columns .project-form-group label {
  font-size: 12px;
}

.project-members-files-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  align-items: start;
  margin-top: 5px;
  min-height: 120px;
}

.project-members-files-row .project-form-group {
  min-width: 0;
}

.project-members-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6px;
  margin-top: 0;
}

.project-member-avatars {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  min-height: 30px;
}

.project-members-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  overflow: visible;
  border: 1px solid #e0e0e0;
  position: relative;
  cursor: pointer;
  margin-right: 6px;
}

.project-members-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.project-remove-member-btn {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  border: none;
  background: #ff4757;
  color: white;
  font-size: 9px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  z-index: 10;
}

.project-members-avatar:hover .project-remove-member-btn {
  transform: scale(1.1);
}

.project-remove-member-btn:hover {
  background: #ff3742;
  transform: scale(1.1);
}

.project-add-member-btn {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 1px solid #fff;
  background: #fff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  padding: 0;
  outline: none;
}
.project-add-member-btn img {
  width: 16px;
  height: 16px;
  filter: invert(50%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(90%) contrast(100%);
}
.project-add-member-btn:hover img {
  filter: invert(36%) sepia(92%) saturate(3800%) hue-rotate(190deg) brightness(97%) contrast(101%);
}

.project-add-follower-btn {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 1px solid #fff;
  background: #fff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  padding: 0;
  outline: none;
}
.project-add-follower-btn img {
  width: 16px;
  height: 16px;
  filter: invert(50%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(90%) contrast(100%);
}
.project-add-follower-btn:hover img {
  filter: invert(36%) sepia(92%) saturate(3800%) hue-rotate(190deg) brightness(97%) contrast(101%);
}

.project-followers-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6px;
  margin-top: 0;
}

.project-followers-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  overflow: visible;
  border: 1px solid #e0e0e0;
  position: relative;
  cursor: pointer;
  margin-right: 6px;
}

.project-followers-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.project-remove-follower-btn {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  border: none;
  background: #ff4757;
  color: white;
  font-size: 9px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  z-index: 10;
}

.project-followers-avatar:hover .project-remove-follower-btn {
  transform: scale(1.1);
}

.project-remove-follower-btn:hover {
  background: #ff3742;
  transform: scale(1.1);
}

.project-file-label-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.project-file-section {
  min-height: 50px;
  width: 100%;
  position: relative;
}

.project-file-label-section label {
  margin: 0;
}

.project-file-items {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-top: 0;
}
.project-file-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px;
  background: #f8f9ff;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  position: relative;
  width: 100%;
  box-sizing: border-box;
}

.project-add-file-btn {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 1px solid #fff;
  background: #fff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  padding: 0;
  outline: none;
}
.project-add-file-btn img {
  width: 16px;
  height: 16px;
  filter: invert(50%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(90%) contrast(100%);
}
.project-add-file-btn:hover img {
  filter: invert(36%) sepia(92%) saturate(3800%) hue-rotate(190deg) brightness(97%) contrast(101%);
}

.project-file-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-height: 65px;
  overflow-y: auto;
  padding-right: 4px;
  width: calc(100% - 26px);
  box-sizing: border-box;
}

.project-file-list::-webkit-scrollbar {
  width: 4px;
}

.project-file-list::-webkit-scrollbar-track {
  background: transparent;
}

.project-file-list::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 2px;
}

.project-file-list::-webkit-scrollbar-thumb:hover {
  background: #bbb;
}



.project-file-icon {
  width: 14px;
  height: 14px;
  flex-shrink: 0;
}

.project-file-name {
  font-size: 12px;
  color: #333;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: calc(100% - 40px);
}

.project-remove-file-btn {
  width: 16px;
  height: 16px;
  border: none;
  background: #ff4757;
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  transition: all 0.2s ease;
  flex-shrink: 0;
  margin-left: auto;
}

.project-remove-file-btn:hover {
  background: #ff3742;
  transform: scale(1.1);
}

.project-remove-file-btn:active {
  transform: scale(0.95);
}

.project-form-action {
  display: flex;
  justify-content: center;
  padding-top: 10px;
  width: 100%;
}

.project-create-btn {
  background: #007bff;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 8px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s, box-shadow 0.2s;
  width: 100%;
  box-shadow: 0 2px 8px rgba(0,123,255,0.07);
}
.project-create-btn:hover {
  background: #0056b3;
  box-shadow: 0 4px 16px rgba(0,123,255,0.13);
}
.project-create-btn:active {
  transform: translateY(1px);
}

/* Responsive design */
@media (max-width: 768px) {
  .project-create-container {
    width: 100%;
    margin: 0 4px;
    padding: 10px;
  }
  
  .project-form-row, .project-form-row.project-three-columns {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  
  .project-member-avatars {
    flex-wrap: wrap;
  }
  .project-create-btn {
    min-width: 100%;
    font-size: 15px;
    padding: 10px 0;
  }
}

/* Date input styling - ProjectCreate specific */
.project-date-input-group {
  display: flex;
  align-items: center;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 0 8px;
  height: 32px;
  min-width: 180px;
  color: #666;
  font-size: 13px;
  position: relative;
  gap: 6px;
  transition: border-color 0.2s ease;
}

.project-date-input-group:hover {
  border-color: #bdbdbd;
}

.project-date-input-group:focus-within {
  border-color: #007bff;
}

.project-date-input-group input {
  border: none;
  outline: none;
  background: transparent;
  color: #333;
  font-size: 15px;
  width: 100%;
  cursor: pointer;
}

.project-date-input-group input[type="date"] {
  border: none;
  outline: none;
  background: transparent;
  color: #333;
  font-size: 12px;
  width: 100%;
  cursor: pointer;
}

.project-date-input-group input[type="date"]::-webkit-calendar-picker-indicator {
  display: none;
}

.project-date-input-group input::placeholder {
  color: #bdbdbd;
}

.project-date-input-group .project-calendar-icon {
  width: 16px;
  height: 16px;
  opacity: 1;
  color: #5B5B5B;
}

/* Field validation styles */
.project-form-group input.project-error,
.project-form-group textarea.project-error,
.project-date-input-group.project-error,
.project-form-group select.project-error {
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.12);
}

.project-field-error {
  color: #dc3545;
  font-size: 11px;
  margin-top: 1px;
  display: block;
  min-height: 12px;
  line-height: 12px;
}

.project-error-message {
  color: #dc3545;
  background-color: #f8d7da;
  border: 1.5px solid #dc3545;
  border-radius: 4px;
  padding: 10px 14px;
  margin-bottom: 16px;
  font-size: 15px;
  font-weight: 500;
  word-break: break-word;
  box-shadow: 0 2px 8px rgba(220,53,69,0.07);
}

/* Reserve space for error messages to prevent jumping */
/* .project-form-group {
  margin-bottom: 20px;
} */

.project-form-group .project-field-error {
  height: 16px;
  margin-bottom: 4px;
}

.project-form-group select {
  padding: 6px 8px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 12px;
  font-family: 'Be Vietnam Pro', Arial, sans-serif;
  background: #fff;
  color: #000;
  transition: border-color 0.2s ease;
  height: 32px;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 16px;
  padding-right: 28px;
  cursor: pointer;
}

.project-form-group select:focus {
  outline: none;
  border-color: #007bff;
}

/* Custom select dropdown styling */
select {
  cursor: pointer;
}

/* Styling for dropdown options */
select option {
  padding: 8px 12px;
  font-size: 12px;
  color: #5B5B5B;
  background-color: #fff;
}

/* Webkit browsers custom styling */
select::-webkit-scrollbar {
  width: 4px;
}

select::-webkit-scrollbar-track {
  background: #f1f1f1;
}

select::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10px;
}

select::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Firefox custom styling */
select {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

/* Custom dropdown styling using datalist */
.custom-select {
  position: relative;
  display: inline-block;
  width: 100%;
}

.custom-select-options {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 999;
  margin-top: 2px;
  padding: 0;
  list-style: none;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
}

.custom-select-options::-webkit-scrollbar {
  width: 4px;
}

.custom-select-options::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.custom-select-options::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10px;
}

.custom-select-option {
  padding: 10px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.custom-select-option:hover {
  background-color: #f5f5f5;
}

.custom-select-option.selected {
  background-color: #f0f7ff;
  color: #007bff;
}

.custom-select-option:first-child {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

.custom-select-option:last-child {
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}

/* Dropdown menu styling */
.project-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 4px;
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
}

.project-dropdown-item {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 12px;
  color: #5B5B5B;
  transition: background-color 0.2s;
}

.project-dropdown-item:hover {
  background-color: #f5f5f5;
}

.project-dropdown-item:first-child {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.project-dropdown-item:last-child {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.project-dropdown::-webkit-scrollbar {
  width: 4px;
}

.project-dropdown::-webkit-scrollbar-track {
  background: transparent;
}

.project-dropdown::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10px;
}

.project-dropdown::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Firefox */
.project-dropdown {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

/* Thêm class error và field-error không có tiền tố để tương thích với code JS hiện tại */
.error {
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.12);
}

.field-error {
  color: #dc3545;
  font-size: 11px;
  margin-top: 1px;
  display: block;
  min-height: 12px;
  line-height: 12px;
}

.error-message {
  color: #dc3545;
  background-color: #f8d7da;
  border: 1.5px solid #dc3545;
  border-radius: 4px;
  padding: 10px 14px;
  margin-bottom: 16px;
  font-size: 15px;
  font-weight: 500;
  word-break: break-word;
  box-shadow: 0 2px 8px rgba(220,53,69,0.07);
}

/* Làm cho dấu * có màu đỏ */
label > span.required-asterisk {
  color: #dc3545;
}

.project-form-row.project-three-columns .project-form-group .project-field-error {
  font-size: 10px;
  min-height: 12px;
  line-height: 12px;
}
