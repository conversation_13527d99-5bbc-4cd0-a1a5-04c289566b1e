@import url('../index.css');
.create-note-modal {
  background: rgba(0,0,0,0.04);
  position: fixed;
  top: 0; left: 0; width: 100vw; height: 100vh;
  display: flex; align-items: center; justify-content: center;
  z-index: 1000;
}
.create-note-form {
  background: #fff;
  border-radius: 14px;
  padding: 20px;
  width: 500px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.10);
  position: relative;
}
.create-note-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #666;
}

.create-note-input:focus, .create-note-textarea:focus {
  border: 1.5px solid #1976d2;
  outline: none;
}
.create-note-input::placeholder, .create-note-textarea::placeholder {
  color: #b0b8c1;
  opacity: 1;
}
.create-note-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 18px;
}
.create-note-close {
  background: none;
  border: none;
  cursor: pointer;
  transition: opacity 0.2s;
  padding: 0;
  margin-top: -4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.create-note-close img {
  width: 20px;
  height: 20px;
}
.create-note-close:hover {
  opacity: 0.7;
}
.create-note-input-container {
  position: relative;
  margin-bottom: 24px;
  height: auto;
}
.create-note-textarea-container {
  position: relative;
  margin-bottom: 24px;
  height: auto;
}
.create-note-input,
.create-note-textarea {
  width: 100%;
  padding: 10px 14px;
  border-radius: 8px;
  border: 1px solid #e0e3e7;
  font-size: 14px;
  font-family: 'Be Vietnam Pro', Arial, sans-serif;
  background: #fff;
  transition: border 0.2s;
  box-sizing: border-box;
  color: #333;
  margin-bottom: 0;
}
.create-note-textarea {
  min-height: 120px;
  resize: none;
}

.create-note-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 10px;
}

.create-note-cancel {
  background: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}
.create-note-cancel:hover {
  background: #e9e9e9;
}

.create-note-submit {
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}

.create-note-submit:hover {
  background: #1565c0;
}

.create-note-input-error {
  border: 1px solid #f44336 !important;
}

.create-note-error-message {
  color: #f44336;
  font-size: 12px;
  margin-top: 4px;
  font-weight: 500;
  height: 16px;
  display: block;
  position: absolute;
}

/* File upload styles */
.file-upload-custom {
  width: 500px !important;
  max-width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px dashed #d3d3d3;
  border-radius: 12px;
  background: #fafafa;
  min-height: 140px;
  cursor: pointer;
  margin-top: 8px;
  margin-bottom: 12px;
  padding: 16px 0;
}
.job-panel-file-upload-custom:hover {
  border-color: #007BFF;
}

.job-panel-file-upload-custom:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.job-panel-file-upload-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 8px;
}

.job-panel-file-upload-text {
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

.job-panel-file-list {
  margin-top: 12px;
  width: 100%;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 7px 12px;
  background: #F0F4FF;
  border-radius: 6px;
  margin-bottom: 6px;
  /* border: 1px solid #e9ecef; */
}

.job-panel-file-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  flex-shrink: 0;
}

.job-panel-file-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.job-panel-file-name {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.job-panel-file-size {
  font-size: 11px;
  color: #666;
  margin-top: 2px;
}

.job-panel-remove-file-btn {
  background: none;
  border: none;
  color: #dc3545;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 4px;
  transition: background 0.2s;
  flex-shrink: 0;
}

.job-panel-remove-file-btn:hover {
  background: #f8d7da;
}

.job-panel-remove-file-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Disabled state styles */
.create-note-input:disabled,
.create-note-textarea:disabled {
  background: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

.create-note-submit:disabled,
.create-note-cancel:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.create-note-submit:disabled:hover,
.create-note-cancel:disabled:hover {
  background: inherit;
}
